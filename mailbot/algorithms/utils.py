import logging
from typing import Optional, Type
from mailbot.algorithms.base import BaseAlgorithm
from mailbot.algorithms.constants import ALGORITHM_MAP, DEFAULT_ALGORITHM_VERSION
from mailbot.models import AlgorithmDefinition, UserMailBotProfile

logger = logging.getLogger(__name__)


def get_algorithm(user_profile: UserMailBotProfile) -> BaseAlgorithm:
    algo_def_to_use = user_profile.algorithmDefinition
    algorithm: Optional[Type[BaseAlgorithm]] = None

    if algo_def_to_use and algo_def_to_use.is_active and algo_def_to_use.version_code in ALGORITHM_MAP:
        algorithm = ALGORITHM_MAP[algo_def_to_use.version_code]
    else:
        warning_message = f"Algorithm version '{algo_def_to_use.version_code if algo_def_to_use else 'None'}' "
        warning_message += "is inactive." if algo_def_to_use and not algo_def_to_use.is_active else "not found."
        logger.warning(f"Warning: Falling back to default algorithm: {DEFAULT_ALGORITHM_VERSION} {warning_message}")
        algorithm = ALGORITHM_MAP[DEFAULT_ALGORITHM_VERSION]
        algo_def_to_use = get_default_active_algorithm()
    return algorithm(algorithm_definition=algo_def_to_use)


def get_default_active_algorithm() -> AlgorithmDefinition:
    try:
        return AlgorithmDefinition.objects.get(version_code=DEFAULT_ALGORITHM_VERSION, is_active=True)
    except AlgorithmDefinition.DoesNotExist:
        logger.warning(f"Warning: Default active algorithm '{DEFAULT_ALGORITHM_VERSION}' not found.")
        raise ValueError(f"Default active algorithm definition '{DEFAULT_ALGORITHM_VERSION}' not found.")
